# STT Report Entity Expansion - Level 3 Only Processing

## Overview

The STT Report controller has been updated to ensure that **only Level 3 entities** are used for assignments and data processing. Level 1 and Level 2 entities are automatically expanded to their corresponding Level 3 children.

## Entity Processing Logic

### 1. Validation Phase
- **Same-level validation**: All input entities must be at the same level (no multi-level filtering)
- **Entity existence validation**: All entity IDs must exist in the system
- **Format validation**: Entities must follow "level-id" format (e.g., "1-12", "2-56", "3-72")

### 2. Expansion Phase
- **Level 1 entities**: Expanded to all their Level 3 children
- **Level 2 entities**: Expanded to all their Level 3 children  
- **Level 3 entities**: Kept as-is (no expansion needed)

### 3. Processing Phase
- **Only Level 3 entities** are used for assignments, data fetching, and reporting
- **Duplicates removed**: If multiple Level 1/2 entities expand to the same Level 3, duplicates are eliminated

## Example Scenarios

### Scenario 1: Level 1 Entity Input
**Input:**
```json
{
  "entity": ["1-46"]
}
```

**Processing:**
1. Validate that "1-46" exists and is a valid Level 1 entity
2. Expand "1-46" to all its Level 3 children: ["3-71", "3-72", "3-73"]
3. Use only Level 3 entities ["3-71", "3-72", "3-73"] for data processing

### Scenario 2: Level 2 Entity Input
**Input:**
```json
{
  "entity": ["2-56", "2-57"]
}
```

**Processing:**
1. Validate that both "2-56" and "2-57" exist and are valid Level 2 entities
2. Expand "2-56" to its Level 3 children: ["3-71", "3-72"]
3. Expand "2-57" to its Level 3 children: ["3-73", "3-74"]
4. Use only Level 3 entities ["3-71", "3-72", "3-73", "3-74"] for data processing

### Scenario 3: Level 3 Entity Input
**Input:**
```json
{
  "entity": ["3-71", "3-72"]
}
```

**Processing:**
1. Validate that both "3-71" and "3-72" exist and are valid Level 3 entities
2. No expansion needed (already Level 3)
3. Use Level 3 entities ["3-71", "3-72"] for data processing

### Scenario 4: Multi-Level Input (REJECTED)
**Input:**
```json
{
  "entity": ["1-46", "3-71"]
}
```

**Result:**
```json
{
  "status": false,
  "message": "All entities must be at the same level. Found entities at different levels: level 1: 1 entities, level 3: 1 entities. Please ensure all entities are at the same level (e.g., all level 1, or all level 2, or all level 3).",
  "error_code": 1004,
  "error_name": "DependencyValidationError",
  "field": "entity"
}
```

## Benefits

### 1. **Consistent Data Processing**
- All assignments and data operations work exclusively with Level 3 entities
- Eliminates complexity of handling different entity levels in downstream processing

### 2. **Simplified Assignment Logic**
- Assignment tables only need to store Level 3 entity references
- No need to handle hierarchical lookups during data retrieval

### 3. **Improved Performance**
- Direct Level 3 entity queries are faster than hierarchical traversals
- Reduced complexity in data aggregation and reporting

### 4. **Clear Data Boundaries**
- Level 3 entities represent the most granular operational units
- Ensures consistent granularity across all reports and analytics

## Implementation Details

### Entity Expansion Map Structure
```typescript
// Level 1 to Level 3 mapping
level1ToLevel3Map: Map<string, string[]> = {
  "1-46" => ["3-71", "3-72", "3-73"],
  "1-47" => ["3-74", "3-75", "3-76"]
}

// Level 2 to Level 3 mapping  
level2ToLevel3Map: Map<string, string[]> = {
  "2-56" => ["3-71", "3-72"],
  "2-57" => ["3-73", "3-74"]
}
```

### Validation and Expansion Flow
1. **Parse and validate entity format** (level-id pattern)
2. **Check entity existence** in respective level validation sets
3. **Enforce same-level requirement** across all input entities
4. **Expand entities** based on their level:
   - Level 1 → All Level 3 descendants
   - Level 2 → All Level 3 descendants  
   - Level 3 → Keep as-is
5. **Remove duplicates** from final Level 3 entity list
6. **Update entity array** with expanded Level 3 entities

## API Documentation Updates

The API schema has been updated to clearly indicate the expansion behavior:

> "Level 1 and Level 2 entities will be expanded to their Level 3 children. Only Level 3 entities are used for assignments and data processing."

This ensures that API consumers understand that regardless of the input entity level, the system will always work with Level 3 entities internally.
