# JWT Authentication Implementation for STT Report Controller

## Overview

The STT Report controller now includes comprehensive JWT authentication and authorization with the following requirements:

1. **JWT Authentication**: Bearer token validation using `id` field from JWT payload
2. **Client Validation**: User must belong to clientId = 17
3. **Role Authorization**: User must have role 24 or 6

## Implementation Details

### 1. **Authentication Flow**

```typescript
// 1. Extract Bearer token from Authorization header
const authHeader = request.headers.authorization;

// 2. Validate token format and decode JWT
const authValidation = await this.validateAuthentication(authHeader);

// 3. Check user authorization (clientId and roles)
const authorizationValidation = await this.validateAuthorization(userProfileId);
```

### 2. **Authentication Method**

```typescript
private async validateAuthentication(authHeader?: string): Promise<{isValid: boolean; userProfileId?: number; error?: any}> {
  // Validates:
  // - Authorization header format: "Bearer <jwt_token>"
  // - JWT token presence and format
  // - Extracts 'id' field from JWT payload
  // - Looks up user by 'userId' field in UserProfile table
  // - Returns userProfileId for authorization check
}
```

### 3. **Authorization Method**

```typescript
private async validateAuthorization(userProfileId: number): Promise<{isValid: boolean; error?: any}> {
  // Validates:
  // - User belongs to clientId = 17
  // - User has role 24 or 6 in UserRoleAuthorization table
}
```

## API Usage Examples

### ✅ **Successful Request**

```bash
curl -X POST "http://localhost:3000/stt-report/generate-report" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..." \
  -H "Content-Type: application/json" \
  -d '{
    "main_data_source": "quantitative",
    "sub_data_source": "raw",
    "raw_parameters": {
      "dcf_name": [123],
      "sub_status": "live"
    },
    "filter_type": "applied_common_filter",
    "applied_common_filter": {
      "year": [2024],
      "reporting_period": "Quarterly",
      "entity": ["3-71"]
    },
    "type_of_data": "direct_extract",
    "type_of_format": "value_field"
  }'
```

**Response:**
```json
{
  "status": true,
  "message": "Report generated successfully",
  "data": { /* report data */ },
  "missing_data": [ /* missing data info */ ]
}
```

### ❌ **Authentication Errors**

#### Missing Authorization Header
```bash
curl -X POST "http://localhost:3000/stt-report/generate-report" \
  -H "Content-Type: application/json" \
  -d '{ /* request body */ }'
```

**Response:**
```json
{
  "status": false,
  "message": "Authorization header missing or invalid format. Expected: Bearer <jwt_token>",
  "error_code": 1001,
  "error_name": "AuthenticationError"
}
```

#### Invalid Token Format
```bash
curl -X POST "http://localhost:3000/stt-report/generate-report" \
  -H "Authorization: InvalidToken123" \
  -H "Content-Type: application/json" \
  -d '{ /* request body */ }'
```

**Response:**
```json
{
  "status": false,
  "message": "Authorization header missing or invalid format. Expected: Bearer <jwt_token>",
  "error_code": 1001,
  "error_name": "AuthenticationError"
}
```

#### User Not Found
```bash
curl -X POST "http://localhost:3000/stt-report/generate-report" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJub25leGlzdGVudCJ9..." \
  -H "Content-Type: application/json" \
  -d '{ /* request body */ }'
```

**Response:**
```json
{
  "status": false,
  "message": "User not found or invalid authentication",
  "error_code": 1001,
  "error_name": "AuthenticationError"
}
```

### ❌ **Authorization Errors**

#### Wrong Client ID
```bash
curl -X POST "http://localhost:3000/stt-report/generate-report" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..." \
  -H "Content-Type: application/json" \
  -d '{ /* request body */ }'
```

**Response:**
```json
{
  "status": false,
  "message": "Access denied: User does not belong to the required client (clientId: 17)",
  "error_code": 1003,
  "error_name": "AuthorizationError"
}
```

#### Insufficient Permissions
```bash
curl -X POST "http://localhost:3000/stt-report/generate-report" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..." \
  -H "Content-Type: application/json" \
  -d '{ /* request body */ }'
```

**Response:**
```json
{
  "status": false,
  "message": "Access denied: User does not have required permissions (roles 24 or 6)",
  "error_code": 1003,
  "error_name": "AuthorizationError"
}
```

## Security Features

### 1. **JWT Token Validation**
- Validates Bearer token format
- Decodes JWT payload to extract user identifier (id)
- Looks up user by userId field

### 2. **Client-Based Access Control**
- Ensures user belongs to specific client (clientId = 17)
- Prevents cross-client data access

### 3. **Role-Based Authorization**
- Checks UserRoleAuthorization table for user permissions
- Requires role 24 or 6 for STT report access
- Supports array-based role storage in database

### 4. **Error Handling**
- Standardized error codes and messages
- Distinguishes between authentication and authorization failures
- Graceful handling of malformed tokens and missing users

## Database Integration

### UserProfile Table
- **userId**: Maps to JWT 'id' claim (e.g., "a6bc991f-4f24-4654-a3fb-fdfada780fb6")
- **clientId**: Must equal 17 for access
- **id**: Used for role lookup

### UserRoleAuthorization Table
- **userProfileId**: Links to UserProfile.id
- **roles**: Array field containing role IDs
- **Required roles**: 24 or 6

## Implementation Benefits

1. **Security**: Comprehensive authentication and authorization
2. **Flexibility**: Supports multiple roles and clients
3. **Scalability**: Role-based access control for future expansion
4. **Maintainability**: Clear separation of authentication and authorization logic
5. **User Experience**: Detailed error messages for troubleshooting

This implementation ensures that only authenticated users with proper permissions can access the STT Report functionality, providing robust security for sensitive sustainability data.
