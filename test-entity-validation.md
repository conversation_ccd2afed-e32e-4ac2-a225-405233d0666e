# STT Report Entity Validation Test Cases

## Current Implementation Behavior

The STT Report controller now enforces strict entity validation with two key rules:

### Rule 1: No Multi-Level Filtering
All entities in a request must be at the same level (1, 2, or 3).

### Rule 2: No Invalid Entity IDs  
If even one entity ID is invalid, the entire request is rejected.

## Test Cases

### ❌ Multi-Level Filtering (REJECTED)
```json
{
  "entity": ["1-46", "3-71"]
}
```
**Expected Error**: "All entities must be at the same level. Found entities at different levels: level 1: 1 entities, level 3: 1 entities. Please ensure all entities are at the same level (e.g., all level 1, or all level 2, or all level 3)."

### ❌ Invalid Entity ID (REJECTED)
```json
{
  "entity": ["1-46", "1-999"]
}
```
**Expected Error**: "Invalid location IDs found: 1-999. These location IDs do not exist in the system. Please verify the location IDs are correct."

### ❌ Invalid Format (REJECTED)
```json
{
  "entity": ["1-46", "invalid-format"]
}
```
**Expected Error**: "Invalid entity format found: invalid-format. Entities must be in format 'level-id' (e.g., '1-12', '2-56', '3-72')."

### ❌ Invalid Level (REJECTED)
```json
{
  "entity": ["1-46", "4-71"]
}
```
**Expected Error**: "Invalid entity level found: 4-71. Level must be 1, 2, or 3 (e.g., '1-12', '2-56', '3-72')."

### ✅ Valid Same-Level Entities (ACCEPTED)
```json
{
  "entity": ["1-46", "1-47"]
}
```

```json
{
  "entity": ["2-56", "2-57"]
}
```

```json
{
  "entity": ["3-71", "3-72"]
}
```

## Implementation Details

The validation is performed by the `validateEntitiesWithShapedSite` method which:

1. **Builds a validation map** from the location hierarchy (locationOnes → locationTwos → locationThrees)
2. **Validates each entity** for format, level, and existence
3. **Collects validation errors** and returns specific error messages
4. **Checks level consistency** across all valid entities
5. **Rejects the entire request** if any validation fails

## API Documentation Updated

The API schema descriptions have been updated to reflect these validation rules:

- "All entities must be at the same level - multi-level filtering is not supported (e.g., cannot mix '1-46' and '3-71')"
- "Must be valid locations in format 'level-id' (e.g., '1-12', '2-56', '3-72')"
