# STT Report Error Codes Implementation

## Overview

The STT Report controller now implements standardized error handling with specific HTTP status codes and application error codes as per the specification.

## Error Code Specification

| Category | HTTP Code | App Error Code | Error Name | Description |
|----------|-----------|----------------|------------|-------------|
| Validation Errors | 400 | 1000 | ValidationError | Basic Validation Failure |
| Validation Errors | 400 | 1001 | MissingFieldError | Required field is missing |
| Validation Errors | 400 | 1002 | InvalidFormatError | Field format is incorrect (e.g., date/email) |
| Validation Errors | 400 | 1003 | ValueOutOfRangeError | Input value exceeds allowed range |
| Validation Errors | 400 | 1004 | DependencyValidationError | Field value is invalid due to another field |

## Implementation Details

### Error Response Structure

All validation errors now return a standardized response format:

```json
{
  "status": false,
  "message": "Detailed error message",
  "error_code": 1001,
  "error_name": "MissingFieldError",
  "field": "main_data_source"
}
```

### Example Error Scenarios

#### 1. Missing Required Field (Error Code 1001)
**Request:**
```json
{
  "filter_type": "applied_common_filter",
  "type_of_data": "direct_extract",
  "type_of_format": "value_field"
  // main_data_source is missing
}
```

**Response:**
```json
{
  "status": false,
  "message": "main_data_source is required",
  "error_code": 1001,
  "error_name": "MissingFieldError",
  "field": "main_data_source"
}
```

#### 2. Invalid Entity Format (Error Code 1002)
**Request:**
```json
{
  "applied_common_filter": {
    "entity": ["invalid-format", "1-46"]
  }
}
```

**Response:**
```json
{
  "status": false,
  "message": "Invalid entity format found: invalid-format. Entities must be in format \"level-id\" (e.g., \"1-12\", \"2-56\", \"3-72\").",
  "error_code": 1002,
  "error_name": "InvalidFormatError",
  "field": "entity"
}
```

#### 3. Invalid Entity Level (Error Code 1003)
**Request:**
```json
{
  "applied_common_filter": {
    "entity": ["4-71", "1-46"]
  }
}
```

**Response:**
```json
{
  "status": false,
  "message": "Invalid entity level found: 4-71. Level must be 1, 2, or 3 (e.g., \"1-12\", \"2-56\", \"3-72\").",
  "error_code": 1003,
  "error_name": "ValueOutOfRangeError",
  "field": "entity"
}
```

#### 4. Multi-Level Entity Validation (Error Code 1004)
**Request:**
```json
{
  "applied_common_filter": {
    "entity": ["1-46", "3-71"]
  }
}
```

**Response:**
```json
{
  "status": false,
  "message": "All entities must be at the same level. Found entities at different levels: level 1: 1 entities, level 3: 1 entities. Please ensure all entities are at the same level (e.g., all level 1, or all level 2, or all level 3).",
  "error_code": 1004,
  "error_name": "DependencyValidationError",
  "field": "entity"
}
```

#### 5. Invalid Location IDs (Error Code 1000)
**Request:**
```json
{
  "applied_common_filter": {
    "entity": ["1-999", "1-46"]
  }
}
```

**Response:**
```json
{
  "status": false,
  "message": "Invalid location IDs found: 1-999. These location IDs do not exist in the system. Please verify the location IDs are correct.",
  "error_code": 1000,
  "error_name": "ValidationError",
  "field": "entity"
}
```

## Benefits

1. **Consistent Error Handling**: All validation errors follow the same response structure
2. **Machine-Readable Error Codes**: Applications can handle specific error types programmatically
3. **Field-Level Error Identification**: The `field` property identifies exactly which field caused the error
4. **Detailed Error Messages**: Human-readable messages provide clear guidance for fixing issues
5. **HTTP Standard Compliance**: Uses appropriate HTTP status codes (400 for validation errors)

## API Documentation Updates

The OpenAPI schema has been updated to include the new error response fields:
- `error_code`: Application-specific error code (1000-1004 for validation errors)
- `error_name`: Error name for programmatic handling
- `field`: Field name that caused the error (for validation errors)

This implementation provides a robust foundation for error handling that can be extended to other controllers in the application.
