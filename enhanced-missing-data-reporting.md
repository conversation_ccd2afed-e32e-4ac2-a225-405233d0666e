# Enhanced Missing Data Reporting - Assignment-Level Periods

## Overview

The STT Report controller now provides enhanced missing data reporting that shows both the requested reporting periods and the underlying assignment-level periods that are missing. This gives users a complete picture of what data is missing at the granular level.

## Key Features

### 1. **Dual-Level Missing Data Reporting**
- **Requested Period**: The period originally requested (e.g., "Q1-2024")
- **Assignment Period**: The underlying assignment periods that contribute to the requested period (e.g., "Jan-2024", "Feb-2024", "Mar-2024")

### 2. **Assignment Frequency Detection**
- Automatically detects the actual assignment frequency for each DCF-Entity combination
- Shows whether assignments are monthly, quarterly, yearly, etc.

### 3. **Granular Missing Data Identification**
- Identifies specific months/periods that are missing within broader reporting periods
- Helps users understand exactly which data submissions are needed

## Enhanced Missing Data Response Structure

```json
{
  "status": true,
  "message": "Report generated successfully",
  "data": { /* report data */ },
  "missing_data": [
    {
      "dcf_id": 123,
      "entity_id": "72",
      "entity": "3-72",
      "reporting_period": "Q1-2024",
      "requested_period": "Q1-2024", 
      "assignment_frequency": "quarterly",
      "reason": "No data found for this DCF-Entity-Period combination"
    },
    {
      "dcf_id": 123,
      "entity_id": "72", 
      "entity": "3-72",
      "reporting_period": "Jan-2024",
      "requested_period": "Q1-2024",
      "assignment_frequency": "monthly",
      "reason": "Missing monthly data that contributes to Quarterly period Q1-2024"
    },
    {
      "dcf_id": 123,
      "entity_id": "72",
      "entity": "3-72", 
      "reporting_period": "Feb-2024",
      "requested_period": "Q1-2024",
      "assignment_frequency": "monthly",
      "reason": "Missing monthly data that contributes to Quarterly period Q1-2024"
    },
    {
      "dcf_id": 123,
      "entity_id": "72",
      "entity": "3-72",
      "reporting_period": "Mar-2024", 
      "requested_period": "Q1-2024",
      "assignment_frequency": "monthly",
      "reason": "Missing monthly data that contributes to Quarterly period Q1-2024"
    }
  ]
}
```

## Example Scenarios

### Scenario 1: Quarterly Request with Monthly Assignments

**Request:**
```json
{
  "applied_common_filter": {
    "reporting_period": "Quarterly",
    "year": [2024],
    "entity": ["3-72"]
  },
  "raw_parameters": {
    "dcf_name": [123]
  }
}
```

**Assignment Configuration:**
- DCF 123 assigned to Entity 3-72 with **monthly** frequency

**Missing Data Response:**
- Shows missing Q1-2024 as requested period
- Shows missing Jan-2024, Feb-2024, Mar-2024 as underlying monthly periods
- Clearly indicates these are monthly assignments contributing to quarterly reporting

### Scenario 2: Yearly Request with Quarterly Assignments  

**Request:**
```json
{
  "applied_common_filter": {
    "reporting_period": "Yearly", 
    "year": [2024],
    "entity": ["3-72"]
  }
}
```

**Assignment Configuration:**
- DCF 456 assigned to Entity 3-72 with **quarterly** frequency

**Missing Data Response:**
- Shows missing 2024 as requested period
- Shows missing Q1-2024, Q2-2024, Q3-2024, Q4-2024 as underlying quarterly periods
- Indicates these are quarterly assignments contributing to yearly reporting

### Scenario 3: Monthly Request with Monthly Assignments

**Request:**
```json
{
  "applied_common_filter": {
    "reporting_period": "Monthly",
    "reporting_period_from": "2024-04",
    "reporting_period_to": "2024-06", 
    "entity": ["3-72"]
  }
}
```

**Assignment Configuration:**
- DCF 789 assigned to Entity 3-72 with **monthly** frequency

**Missing Data Response:**
- Shows missing Apr-2024, May-2024, Jun-2024 as both requested and assignment periods
- No underlying periods since request matches assignment frequency

## Benefits

### 1. **Complete Visibility**
Users can see exactly which data submissions are missing at the most granular level, making it easier to identify and address data gaps.

### 2. **Assignment-Aware Reporting**
The system understands the actual assignment frequencies and shows missing data accordingly, preventing confusion about what data is expected.

### 3. **Actionable Information**
Users know exactly which periods need data submission, whether it's monthly data for quarterly reporting or quarterly data for yearly reporting.

### 4. **Frequency Mismatch Detection**
Clearly shows when requested reporting periods don't align with assignment frequencies, helping users understand data collection patterns.

## Implementation Details

### Assignment Frequency Detection
- Queries `AssignDcfEntityUser` table to get actual assignment frequencies
- Maps frequency codes to human-readable names (1=monthly, 2=bi-monthly, etc.)
- Builds assignment map for quick lookup during missing data calculation

### Period Expansion Logic
- **Quarterly → Monthly**: Q1-2024 expands to Jan-2024, Feb-2024, Mar-2024
- **Half-Yearly → Monthly**: H1-2024 expands to Jan-2024 through Jun-2024  
- **Yearly → Monthly**: 2024 expands to Jan-2024 through Dec-2024
- **Same Frequency**: No expansion needed

### Error Handling
- Gracefully handles missing assignment information
- Defaults to 'Unknown' frequency if assignment not found
- Continues processing even if some assignments can't be determined

This enhanced missing data reporting provides users with the detailed information they need to understand and address data gaps in their sustainability reporting workflow.
