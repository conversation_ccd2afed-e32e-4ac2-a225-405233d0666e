# Updated JWT Authentication Test - ClientId 17

## JWT Payload Structure

Based on your JWT payload:
```json
{
  "id": "a6bc991f-4f24-4654-a3fb-fdfada780fb6",
  "name": null,
  "email": "<EMAIL>",
  "iat": 1750520359,
  "exp": 1750541959
}
```

## Updated Authentication Logic

### 1. **JWT Field Mapping**
- **JWT `id` field** → **UserProfile `userId` field**
- **Required clientId**: 17 (instead of 94)
- **Required roles**: 24 or 6

### 2. **Database Setup**

#### UserProfile Table
```sql
-- Valid user with correct client and UUID
INSERT INTO UserProfile (id, userId, clientId, active, email) 
VALUES (1, 'a6bc991f-4f24-4654-a3fb-fdfada780fb6', 17, true, '<EMAIL>');

-- User with wrong client
INSERT INTO UserProfile (id, userId, clientId, active, email) 
VALUES (2, 'a6bc991f-4f24-4654-a3fb-fdfada780fb6', 18, true, '<EMAIL>');

-- User that doesn't exist
-- (no entry for some other UUID)
```

#### UserRoleAuthorization Table
```sql
-- Valid user with required role 24
INSERT INTO UserRoleAuthorization (userProfileId, roles) 
VALUES (1, '[24]');

-- Alternative: Valid user with required role 6
INSERT INTO UserRoleAuthorization (userProfileId, roles) 
VALUES (1, '[6]');

-- User with both valid roles
INSERT INTO UserRoleAuthorization (userProfileId, roles) 
VALUES (1, '[24, 6]');

-- User with invalid role
INSERT INTO UserRoleAuthorization (userProfileId, roles) 
VALUES (1, '[25]');
```

## Test Scenarios

### ✅ **Valid Request**

**Prerequisites:**
- UserProfile exists with userId = "a6bc991f-4f24-4654-a3fb-fdfada780fb6"
- UserProfile.clientId = 17
- UserRoleAuthorization contains role 24 or 6

**Request:**
```bash
curl -X POST "http://localhost:3000/stt-report/generate-report" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************.signature" \
  -H "Content-Type: application/json" \
  -d '{
    "main_data_source": "quantitative",
    "sub_data_source": "raw",
    "raw_parameters": {
      "dcf_name": [304],
      "sub_status": "live"
    },
    "filter_type": "applied_common_filter",
    "applied_common_filter": {
      "year": [2024],
      "reporting_period": "Quarterly",
      "entity": ["3-71"]
    },
    "type_of_data": "direct_extract",
    "type_of_format": "value_field"
  }'
```

**Expected Response:**
```json
{
  "status": true,
  "message": "Report generated successfully",
  "data": {
    "value": 1234.56,
    "unit": "kg CO2e",
    "period": "Q1-2024"
  },
  "missing_data": []
}
```

### ❌ **User Not Found**

**Prerequisites:**
- JWT contains id that doesn't exist in UserProfile table

**Request:**
```bash
curl -X POST "http://localhost:3000/stt-report/generate-report" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************.signature" \
  -H "Content-Type: application/json" \
  -d '{ /* request body */ }'
```

**Expected Response:**
```json
{
  "status": false,
  "message": "User not found or invalid authentication",
  "error_code": 1001,
  "error_name": "AuthenticationError"
}
```

### ❌ **Wrong Client ID**

**Prerequisites:**
- User exists but has clientId ≠ 17 (e.g., clientId = 18)

**Request:**
```bash
curl -X POST "http://localhost:3000/stt-report/generate-report" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************.signature" \
  -H "Content-Type: application/json" \
  -d '{ /* request body */ }'
```

**Expected Response:**
```json
{
  "status": false,
  "message": "Access denied: User does not belong to the required client (clientId: 17)",
  "error_code": 1003,
  "error_name": "AuthorizationError"
}
```

### ❌ **Insufficient Permissions**

**Prerequisites:**
- User exists with clientId = 17
- User does not have role 24 or 6

**Request:**
```bash
curl -X POST "http://localhost:3000/stt-report/generate-report" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************.signature" \
  -H "Content-Type: application/json" \
  -d '{ /* request body */ }'
```

**Expected Response:**
```json
{
  "status": false,
  "message": "Access denied: User does not have required permissions (roles 24 or 6)",
  "error_code": 1003,
  "error_name": "AuthorizationError"
}
```

### ❌ **Missing JWT ID Field**

**Prerequisites:**
- JWT token missing 'id' field

**JWT Payload:**
```json
{
  "name": "test user",
  "email": "<EMAIL>",
  "iat": 1750520359,
  "exp": 1750541959
}
```

**Expected Response:**
```json
{
  "status": false,
  "message": "Invalid JWT token: missing user identifier (id)",
  "error_code": 1001,
  "error_name": "AuthenticationError"
}
```

## JWT Token Example for Testing

### Valid Token Structure
```json
{
  "header": {
    "alg": "HS256",
    "typ": "JWT"
  },
  "payload": {
    "id": "a6bc991f-4f24-4654-a3fb-fdfada780fb6",
    "name": null,
    "email": "<EMAIL>",
    "iat": 1750520359,
    "exp": 1750541959
  }
}
```

### Test Token Generation (Node.js)
```javascript
const jwt = require('jsonwebtoken');

const validToken = jwt.sign({
  id: 'a6bc991f-4f24-4654-a3fb-fdfada780fb6',
  name: null,
  email: '<EMAIL>',
  iat: Math.floor(Date.now() / 1000),
  exp: Math.floor(Date.now() / 1000) + (60 * 60) // 1 hour
}, 'test-secret');

console.log('Valid Token:', validToken);
```

## Key Changes Summary

1. **JWT Field**: Changed from `sub` to `id`
2. **UserProfile Lookup**: Changed from `cognitoRefUserName` to `userId`
3. **Client ID**: Changed from 94 to 17
4. **UUID Format**: Now supports full UUID format (a6bc991f-4f24-4654-a3fb-fdfada780fb6)

The authentication system now correctly handles your JWT payload structure and validates against clientId 17.
